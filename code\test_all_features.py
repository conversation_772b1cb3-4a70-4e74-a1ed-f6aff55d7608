#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
測試所有新功能
"""

import numpy as np
import cv2
from gait_analyzer import GaitAnalyzer
import visualizer as vis

def test_three_level_evaluation():
    """測試三級評估系統"""
    print("\n=== 測試三級評估系統 ===")
    
    analyzer = GaitAnalyzer()
    
    # 測試案例
    test_cases = [
        # (left_knee, right_knee, speed, expected_level)
        (90, 85, 0.5, "excellent"),  # 優良角度，高速度
        (120, 125, 0.2, "good"),     # 一般角度，中等速度
        (145, 150, 0.05, "poor"),    # 不佳角度，低速度
        (80, 90, 0.15, "good"),      # 優良角度，中等速度
    ]
    
    for i, (left, right, speed, expected) in enumerate(test_cases):
        angles = {'left_knee': left, 'right_knee': right}
        level, score, reason, angle_score, speed_score = analyzer.evaluate_gait(angles, speed)
        
        print(f"測試 {i+1}:")
        print(f"  輸入: 左膝={left}°, 右膝={right}°, 速度={speed}")
        print(f"  結果: {level} (評分: {score:.1f}) {reason}")
        print(f"  預期: {expected}")
        print(f"  {'PASS' if level == expected else 'FAIL'}")
        print()

def test_progress_bar():
    """測試進度條功能"""
    print("\n=== 測試進度條功能 ===")
    
    # 創建測試圖像
    test_image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 測試不同評分的進度條
    test_scores = [
        (90, "excellent"),
        (50, "good"),
        (20, "poor")
    ]
    
    for score, level in test_scores:
        img_copy = test_image.copy()
        img_with_bar = vis.draw_progress_bar(img_copy, score, level)
        print(f"進度條測試: {level} (評分: {score}) - 已繪製")
    
    print("進度條功能測試完成")

def test_config_loading():
    """測試配置載入功能"""
    print("\n=== 測試配置載入功能 ===")
    
    analyzer = GaitAnalyzer()
    
    print("角度閾值配置:")
    print(f"  優良範圍: {analyzer.angle_thresholds['excellent_ranges']}")
    print(f"  一般範圍: {analyzer.angle_thresholds['good_ranges']}")
    print(f"  不佳範圍: {analyzer.angle_thresholds['poor_range']}")
    
    print("\n速度閾值配置:")
    print(f"  優良: {analyzer.speed_thresholds['excellent']}")
    print(f"  一般: {analyzer.speed_thresholds['good']}")
    print(f"  不佳: {analyzer.speed_thresholds['poor']}")
    
    print("\n評分權重:")
    print(f"  角度權重: {analyzer.scoring['angle_weight']}")
    print(f"  速度權重: {analyzer.scoring['speed_weight']}")

def test_enhanced_display():
    """測試增強顯示功能"""
    print("\n=== 測試增強顯示功能 ===")
    
    test_image = np.zeros((480, 640, 3), dtype=np.uint8)
    
    test_cases = [
        ("excellent", 85, 0.4, ""),
        ("good", 55, 0.2, "Speed"),
        ("poor", 25, 0.05, "Both")
    ]
    
    for level, score, speed, reason in test_cases:
        img_copy = test_image.copy()
        img_enhanced = vis.draw_gait_result_enhanced(img_copy, level, score, speed, reason)
        print(f"增強顯示測試: {level} - 已繪製")
    
    print("增強顯示功能測試完成")

def main():
    print("開始測試所有新功能...")
    
    try:
        test_config_loading()
        test_three_level_evaluation()
        test_progress_bar()
        test_enhanced_display()
        
        print("\n" + "="*50)
        print("所有測試完成！")
        print("新功能包括:")
        print("1. 三級步態評估系統 (優良/一般/不佳)")
        print("2. 漸變色進度條顯示")
        print("3. YAML 配置檔案支援")
        print("4. 增強版視覺化顯示")
        print("="*50)
        
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
