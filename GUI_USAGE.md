# GUI Usage Guide - Gait Detection System

## Overview

The Gait Detection System now features a modern PySide6 GUI interface that replaces the previous OpenCV display. This guide explains how to use the new interface and its features.

## New Features

### 1. Modern PySide6 GUI Interface
- Professional graphical user interface with organized layout
- Real-time camera feed display with pose landmark overlay
- Separate progress bars for angle and speed metrics
- Modern styling with consistent color schemes

### 2. Separate Progress Bars
- **Angle Score Progress Bar**: Shows angle evaluation (0-100) with gradient colors
- **Speed Score Progress Bar**: Shows speed evaluation (0-100) with gradient colors
- Both bars use red-yellow-green gradient (red=poor, yellow=good, green=excellent)

### 3. English Interface
- All text labels, buttons, and messages are in English
- Progress bar labels: "Angle Score" and "Speed Score"
- Level indicators: "Excellent", "Good", "Poor"
- Status messages and error messages in English

### 4. Enhanced UI Design
- Modern color scheme with professional styling
- Proper layouts with organized component placement
- Consistent fonts and spacing throughout the interface
- Responsive design that adapts to window resizing

## Installation

### Prerequisites
Make sure you have Python 3.7+ and a virtual environment activated.

### Install Dependencies
```bash
cd walking_tester/code
pip install -r requirements.txt
```

The requirements.txt now includes:
- opencv-python>=4.5.0
- mediapipe>=0.8.0
- numpy>=1.20.0
- PyYAML>=6.0
- PySide6>=6.0.0

## Running the Application

### 1. Check Dependencies
First, verify all dependencies are installed:
```bash
python gui_launcher.py
```

### 2. Run Main GUI Application
```bash
python gui_main.py
```

### 3. Test GUI Components
To test individual GUI components:
```bash
python test_gui_features.py
```

### 4. Simple GUI Test
For a basic interface test:
```bash
python gui_simple.py
```

## Using the GUI

### Main Interface Layout

The main window is divided into several sections:

#### Left Side - Camera Feed
- **Video Display**: Shows live camera feed with pose landmarks
- **Click to Start**: Click on the video area to start/stop the camera
- **Pose Overlay**: When a person is detected, pose landmarks are drawn on the video

#### Right Side - Control Panel
- **Gait Status**: Shows overall gait quality (Excellent/Good/Poor)
- **Angle Score Progress Bar**: Real-time angle evaluation with gradient colors
- **Speed Score Progress Bar**: Real-time speed evaluation with gradient colors
- **Joint Angles**: Displays left and right knee angles in degrees
- **Walking Speed**: Shows current walking speed in m/s

#### Bottom - Status Bar
- Shows current system status and detection information

### Progress Bar Color Scheme

Both progress bars use a gradient color system:
- **0-30%**: Red (Poor performance)
- **30-70%**: Yellow/Orange (Good performance)  
- **70-100%**: Green (Excellent performance)

### Gait Status Indicators

- **Excellent Gait** (Green): Both angle and speed scores are high
- **Good Gait** (Orange): Moderate performance with room for improvement
- **Poor Gait** (Red): Low scores requiring attention

## Configuration

### Settings File
The system uses `settings.yaml` for configuration:

```yaml
gait_analysis:
  angle_thresholds:
    excellent_ranges: [[60, 100], [160, 180]]
    good_ranges: [[100, 130], [130, 160]]
    poor_range: [130, 160]
  speed_thresholds:
    excellent: 0.3
    good: 0.1
    poor: 0.1

gui:
  window:
    title: "Gait Detection System"
    width: 1200
    height: 800
  colors:
    primary: "#2C3E50"
    success: "#27AE60"
    warning: "#F39C12"
    danger: "#E74C3C"
```

### Customizing Thresholds
You can adjust the angle and speed thresholds by editing the `settings.yaml` file. Changes take effect when you restart the application.

## Troubleshooting

### Common Issues

1. **PySide6 Import Error**
   ```bash
   pip install PySide6
   ```

2. **Camera Not Working**
   - Check if camera is connected and not used by other applications
   - Try clicking the video area to restart camera

3. **Poor Performance**
   - Close other applications using the camera
   - Ensure good lighting for pose detection

4. **Configuration Not Loading**
   - Check that `settings.yaml` is in the correct location
   - Verify YAML syntax is correct

### Performance Tips

- Ensure good lighting for optimal pose detection
- Position yourself fully in the camera frame
- Walk naturally for best gait analysis results
- Keep the camera stable and at appropriate height

## Legacy Support

The original OpenCV-based interface is still available:
```bash
python main.py
```

However, the new PySide6 GUI is recommended for the best user experience.

## Development

### Adding Custom Widgets
New GUI components can be added to `gui_widgets.py`. The system includes:
- `GradientProgressBar`: Custom progress bar with gradient colors
- `StatusPanel`: Status display panel
- `MetricDisplay`: Metric value display
- `AngleDisplay`: Joint angle display

### Styling
Custom styles are defined in `styles.qss` and can be modified for different themes or appearances.

### Threading
The GUI uses a separate thread for video processing to maintain responsive interface performance.
