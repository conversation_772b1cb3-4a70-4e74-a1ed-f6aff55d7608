#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PySide6 GUI Main Application for Gait Detection System
"""

import sys
import cv2
import numpy as np
import time
import yaml
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QGridLayout, QLabel, QProgressBar,
                               QFrame, QStatusBar, QSizePolicy)
from PySide6.QtCore import QTimer, Qt, Signal, QThread
from PySide6.QtGui import QImage, QPixmap, QFont, QPalette, QColor

# Import our modules
from pose_detection import PoseDetector
from angle_calculator import AngleCalculator
from gait_analyzer import GaitAnalyzer
import videocapture as vc
from gui_widgets import GradientProgressBar, StatusPanel


class VideoThread(QThread):
    """Thread for handling video processing"""
    frame_ready = Signal(np.ndarray)
    gait_data_ready = Signal(dict)
    
    def __init__(self):
        super().__init__()
        self.running = False
        self.cap = None
        self.pose_detector = PoseDetector()
        self.angle_calculator = AngleCalculator()
        self.gait_analyzer = GaitAnalyzer()
        self.prev_time = time.time()
        
    def start_capture(self):
        """Start video capture"""
        self.cap = vc.open_camera()
        self.running = True
        self.start()
        
    def stop_capture(self):
        """Stop video capture"""
        self.running = False
        if self.cap:
            self.cap.release()
        self.quit()
        self.wait()
        
    def run(self):
        """Main video processing loop"""
        while self.running:
            if self.cap is None:
                continue
                
            frame = vc.get_camera_frame(self.cap)
            if frame is None:
                continue
                
            # Mirror the frame horizontally
            frame = cv2.flip(frame, 1)
            
            # Detect pose
            pose_landmarks = self.pose_detector.detect(frame)
            
            # Prepare gait data
            gait_data = {
                'pose_landmarks': pose_landmarks,
                'has_person': pose_landmarks is not None,
                'angles': {},
                'speed': 0.0,
                'level': 'poor',
                'score': 0.0,
                'angle_score': 0.0,
                'speed_score': 0.0,
                'reason': ''
            }
            
            if pose_landmarks is not None:
                # Calculate angles
                angles = self.angle_calculator.get_joint_angles(pose_landmarks)
                gait_data['angles'] = angles
                
                # Calculate speed
                curr_time = time.time()
                dt = curr_time - self.prev_time
                self.prev_time = curr_time
                speed = self.gait_analyzer.calculate_speed(pose_landmarks, dt)
                gait_data['speed'] = speed
                
                # Evaluate gait
                level, score, reason = self.gait_analyzer.evaluate_gait(angles, speed)
                angle_score = self.gait_analyzer._evaluate_angles(angles)
                speed_score = self.gait_analyzer._evaluate_speed(speed)
                
                gait_data.update({
                    'level': level,
                    'score': score,
                    'angle_score': angle_score,
                    'speed_score': speed_score,
                    'reason': reason
                })
            
            # Emit signals
            self.frame_ready.emit(frame)
            self.gait_data_ready.emit(gait_data)
            
            # Small delay to prevent overwhelming the GUI
            self.msleep(33)  # ~30 FPS


class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.video_thread = VideoThread()
        self.load_config()
        self.init_ui()
        self.setup_connections()
        self.apply_styles()

    def load_config(self):
        """Load configuration from settings.yaml"""
        try:
            with open('../settings.yaml', 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        except FileNotFoundError:
            # Use default config if file not found
            self.config = {
                'system': {
                    'pixel_to_meter': None
                }
            }

        # Get pixel_to_meter setting
        self.pixel_to_meter = self.config.get('system', {}).get('pixel_to_meter', None)
        
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("Gait Detection System")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(800, 600)
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # Left side - Video display
        self.setup_video_panel(main_layout)
        
        # Right side - Control panel
        self.setup_control_panel(main_layout)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Click to start camera")
        
    def setup_video_panel(self, parent_layout):
        """Setup video display panel"""
        video_frame = QFrame()
        video_frame.setFrameStyle(QFrame.Box)
        video_frame.setLineWidth(2)
        video_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        video_layout = QVBoxLayout(video_frame)
        video_layout.setContentsMargins(10, 5, 10, 10)  # 減少上邊距

        # Video title - 縮小字體和間距
        video_title = QLabel("Camera Feed")
        video_title.setAlignment(Qt.AlignCenter)
        video_title.setFont(QFont("Arial", 12, QFont.Bold))  # 從14改為12
        video_title.setMaximumHeight(30)  # 限制標題高度
        video_layout.addWidget(video_title)

        # Video display label
        self.video_label = QLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet("background-color: #2C3E50; color: white; font-size: 16px;")
        self.video_label.setText("Click to start camera")
        self.video_label.mousePressEvent = self.toggle_camera
        video_layout.addWidget(self.video_label)

        parent_layout.addWidget(video_frame, 2)  # 2/3 of the width

    def setup_control_panel(self, parent_layout):
        """Setup control panel with progress bars and status"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.Box)
        control_frame.setLineWidth(2)
        control_frame.setMaximumWidth(420)  # 稍微增加寬度
        control_frame.setMinimumWidth(380)  # 設定最小寬度
        control_frame.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)

        control_layout = QVBoxLayout(control_frame)
        control_layout.setSpacing(15)  # 減少間距以容納更多內容
        control_layout.setContentsMargins(15, 15, 15, 15)  # 設定邊距

        # Title
        title = QLabel("Gait Analysis")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Bold))
        control_layout.addWidget(title)

        # Overall status
        self.setup_overall_status(control_layout)

        # Progress bars
        self.setup_progress_bars(control_layout)

        # Joint angles display
        self.setup_angles_display(control_layout)

        # Speed display
        self.setup_speed_display(control_layout)

        # Stretch to push everything to top
        control_layout.addStretch()

        parent_layout.addWidget(control_frame, 1)  # 1/3 of the width

    def setup_overall_status(self, parent_layout):
        """Setup overall gait status display"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_layout = QVBoxLayout(status_frame)
        status_layout.setContentsMargins(10, 10, 10, 10)  # 減少邊距
        status_layout.setSpacing(5)  # 減少間距

        # Status label
        status_title = QLabel("Gait Status")
        status_title.setFont(QFont("Arial", 11, QFont.Bold))  # 稍微減小字體
        status_title.setAlignment(Qt.AlignCenter)
        status_layout.addWidget(status_title)

        self.status_label = QLabel("No Person Detected")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 13, QFont.Bold))  # 稍微減小字體
        self.status_label.setStyleSheet("color: #E74C3C; padding: 8px;")  # 減少padding
        status_layout.addWidget(self.status_label)

        parent_layout.addWidget(status_frame)

    def setup_progress_bars(self, parent_layout):
        """Setup angle and speed progress bars"""
        progress_frame = QFrame()
        progress_frame.setFrameStyle(QFrame.StyledPanel)
        progress_frame.setMinimumHeight(200)  # 確保有足夠高度
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setContentsMargins(20, 20, 20, 20)  # 增加更多邊距
        progress_layout.setSpacing(12)  # 增加間距

        # Angle Score Progress Bar Section
        angle_section_frame = QFrame()
        angle_section_layout = QVBoxLayout(angle_section_frame)
        angle_section_layout.setContentsMargins(0, 0, 0, 0)
        angle_section_layout.setSpacing(6)

        angle_label = QLabel("Joint Angle Quality")
        angle_label.setFont(QFont("Arial", 12, QFont.Bold))
        angle_label.setStyleSheet("""
            QLabel {
                color: #2C3E50;
                background-color: transparent;
                padding: 5px 0px;
                border: none;
            }
        """)
        angle_label.setToolTip("Evaluates knee joint angle quality during walking")
        angle_label.setMinimumHeight(25)  # 確保標籤有足夠高度
        angle_section_layout.addWidget(angle_label)

        self.angle_progress = GradientProgressBar()
        self.angle_progress.setMinimum(0)
        self.angle_progress.setMaximum(100)
        self.angle_progress.setValue(0)
        self.angle_progress.setMinimumHeight(30)  # 增加進度條高度
        angle_section_layout.addWidget(self.angle_progress)

        self.angle_value_label = QLabel("0 / 100")
        self.angle_value_label.setAlignment(Qt.AlignCenter)
        self.angle_value_label.setFont(QFont("Arial", 10))
        self.angle_value_label.setStyleSheet("color: #2C3E50; padding: 3px;")
        angle_section_layout.addWidget(self.angle_value_label)

        progress_layout.addWidget(angle_section_frame)
        progress_layout.addSpacing(20)  # 兩個進度條之間的間距

        # Speed Score Progress Bar Section
        speed_section_frame = QFrame()
        speed_section_layout = QVBoxLayout(speed_section_frame)
        speed_section_layout.setContentsMargins(0, 0, 0, 0)
        speed_section_layout.setSpacing(6)

        speed_label = QLabel("Walking Speed Quality")
        speed_label.setFont(QFont("Arial", 12, QFont.Bold))
        speed_label.setStyleSheet("""
            QLabel {
                color: #2C3E50;
                background-color: transparent;
                padding: 5px 0px;
                border: none;
            }
        """)
        speed_label.setToolTip("Evaluates walking speed quality and consistency")
        speed_label.setMinimumHeight(25)  # 確保標籤有足夠高度
        speed_section_layout.addWidget(speed_label)

        self.speed_progress = GradientProgressBar()
        self.speed_progress.setMinimum(0)
        self.speed_progress.setMaximum(100)
        self.speed_progress.setValue(0)
        self.speed_progress.setMinimumHeight(30)  # 增加進度條高度
        speed_section_layout.addWidget(self.speed_progress)

        self.speed_value_label = QLabel("0 / 100")
        self.speed_value_label.setAlignment(Qt.AlignCenter)
        self.speed_value_label.setFont(QFont("Arial", 10))
        self.speed_value_label.setStyleSheet("color: #2C3E50; padding: 3px;")
        speed_section_layout.addWidget(self.speed_value_label)

        progress_layout.addWidget(speed_section_frame)
        parent_layout.addWidget(progress_frame)

    def setup_angles_display(self, parent_layout):
        """Setup joint angles display"""
        angles_frame = QFrame()
        angles_frame.setFrameStyle(QFrame.StyledPanel)
        angles_layout = QVBoxLayout(angles_frame)
        angles_layout.setContentsMargins(10, 8, 10, 8)  # 減少邊距
        angles_layout.setSpacing(5)  # 減少間距

        angles_title = QLabel("Joint Angles")
        angles_title.setFont(QFont("Arial", 11, QFont.Bold))  # 稍微減小字體
        angles_title.setAlignment(Qt.AlignCenter)
        angles_layout.addWidget(angles_title)

        # Create labels for angles
        self.left_knee_label = QLabel("Left Knee: 0.0°")
        self.left_knee_label.setFont(QFont("Arial", 10))
        self.left_knee_label.setStyleSheet("color: #2C3E50; padding: 2px;")
        angles_layout.addWidget(self.left_knee_label)

        self.right_knee_label = QLabel("Right Knee: 0.0°")
        self.right_knee_label.setFont(QFont("Arial", 10))
        self.right_knee_label.setStyleSheet("color: #2C3E50; padding: 2px;")
        angles_layout.addWidget(self.right_knee_label)

        parent_layout.addWidget(angles_frame)

    def setup_speed_display(self, parent_layout):
        """Setup speed display"""
        speed_frame = QFrame()
        speed_frame.setFrameStyle(QFrame.StyledPanel)
        speed_layout = QVBoxLayout(speed_frame)
        speed_layout.setContentsMargins(10, 8, 10, 8)  # 減少邊距
        speed_layout.setSpacing(5)  # 減少間距

        speed_title = QLabel("Walking Speed")
        speed_title.setFont(QFont("Arial", 11, QFont.Bold))  # 稍微減小字體
        speed_title.setAlignment(Qt.AlignCenter)
        speed_layout.addWidget(speed_title)

        self.speed_display_label = QLabel("0.00 m/s")
        self.speed_display_label.setAlignment(Qt.AlignCenter)
        self.speed_display_label.setFont(QFont("Arial", 13, QFont.Bold))  # 稍微減小字體
        self.speed_display_label.setStyleSheet("color: #2C3E50; padding: 5px;")
        speed_layout.addWidget(self.speed_display_label)

        parent_layout.addWidget(speed_frame)

    def setup_connections(self):
        """Setup signal connections"""
        self.video_thread.frame_ready.connect(self.update_video_display)
        self.video_thread.gait_data_ready.connect(self.update_gait_data)

    def toggle_camera(self, event=None):
        """Toggle camera on/off"""
        if not self.video_thread.running:
            self.video_thread.start_capture()
            self.status_bar.showMessage("Camera started")
            self.video_label.setText("Loading camera...")
        else:
            self.video_thread.stop_capture()
            self.status_bar.showMessage("Camera stopped")
            self.video_label.setText("Click to start camera")
            self.reset_displays()

    def update_video_display(self, frame):
        """Update video display with new frame"""
        # Draw pose landmarks on frame if available
        if hasattr(self, 'current_pose_landmarks') and self.current_pose_landmarks is not None:
            frame = self.draw_pose_landmarks(frame, self.current_pose_landmarks)

        # Convert frame to QImage
        rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)

        # Scale image to fit label while maintaining aspect ratio
        pixmap = QPixmap.fromImage(qt_image)
        scaled_pixmap = pixmap.scaled(self.video_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.video_label.setPixmap(scaled_pixmap)

    def draw_pose_landmarks(self, frame, pose_landmarks):
        """Draw pose landmarks on frame"""
        import mediapipe as mp
        mp_drawing = mp.solutions.drawing_utils
        mp_pose = mp.solutions.pose

        mp_drawing.draw_landmarks(
            frame,
            pose_landmarks,
            mp_pose.POSE_CONNECTIONS,
            mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
            mp_drawing.DrawingSpec(color=(0, 0, 255), thickness=2)
        )
        return frame

    def update_gait_data(self, gait_data):
        """Update all gait-related displays"""
        self.current_pose_landmarks = gait_data['pose_landmarks']

        if gait_data['has_person']:
            # Update overall status
            level = gait_data['level']
            reason = gait_data['reason']

            level_colors = {
                'excellent': '#27AE60',  # Green
                'good': '#F39C12',       # Orange
                'poor': '#E74C3C'        # Red
            }

            level_text = {
                'excellent': 'Excellent Gait',
                'good': 'Good Gait',
                'poor': 'Poor Gait'
            }

            status_text = level_text.get(level, 'Unknown')
            if reason and level != 'excellent':
                status_text += f" ({reason})"

            self.status_label.setText(status_text)
            self.status_label.setStyleSheet(f"color: {level_colors.get(level, '#E74C3C')}; padding: 10px;")

            # Update progress bars
            angle_score = int(gait_data['angle_score'])
            speed_score = int(gait_data['speed_score'])

            self.angle_progress.setValue(angle_score)
            self.angle_value_label.setText(f"{angle_score} / 100")

            self.speed_progress.setValue(speed_score)
            self.speed_value_label.setText(f"{speed_score} / 100")

            # Update joint angles
            angles = gait_data['angles']
            left_knee = angles.get('left_knee', 0)
            right_knee = angles.get('right_knee', 0)

            self.left_knee_label.setText(f"Left Knee: {left_knee:.1f}°")
            self.right_knee_label.setText(f"Right Knee: {right_knee:.1f}°")

            # Update speed - 根據pixel_to_meter設定決定顯示單位
            speed = gait_data['speed']
            if self.pixel_to_meter is not None:
                # 有實際標定，顯示公尺/秒
                self.speed_display_label.setText(f"{speed:.2f} m/s")
            else:
                # 沒有標定，顯示像素/秒
                self.speed_display_label.setText(f"{speed:.2f} pixels/s")

            self.status_bar.showMessage(f"Person detected - {status_text}")

        else:
            # No person detected
            self.reset_displays()
            self.status_bar.showMessage("No person detected - Please start walking")

    def reset_displays(self):
        """Reset all displays to default values"""
        self.status_label.setText("No Person Detected")
        self.status_label.setStyleSheet("color: #E74C3C; padding: 10px;")

        self.angle_progress.setValue(0)
        self.angle_value_label.setText("0 / 100")

        self.speed_progress.setValue(0)
        self.speed_value_label.setText("0 / 100")

        self.left_knee_label.setText("Left Knee: 0.0°")
        self.right_knee_label.setText("Right Knee: 0.0°")

        # 根據pixel_to_meter設定決定顯示單位
        if self.pixel_to_meter is not None:
            self.speed_display_label.setText("0.00 m/s")
        else:
            self.speed_display_label.setText("0.00 pixels/s")

    def apply_styles(self):
        """Apply custom styles to the application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ECF0F1;
            }
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QLabel {
                color: #2C3E50;
            }
            QStatusBar {
                background-color: #34495E;
                color: white;
                font-weight: bold;
            }
        """)

    def closeEvent(self, event):
        """Handle application close event"""
        if self.video_thread.running:
            self.video_thread.stop_capture()
        event.accept()


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Gait Detection System")
    app.setOrganizationName("Gait Analysis Lab")

    # Set application style
    app.setStyle('Fusion')

    # Create and show main window
    window = MainWindow()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
